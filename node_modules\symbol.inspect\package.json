{"name": "symbol.inspect", "version": "1.0.1", "description": "<symbol> that can be used to declare custom inspect functions.", "keywords": ["nodejs", "inspect", "util", "custom", "symbol"], "homepage": "https://github.com/bluelovers/symbol.inspect#readme", "bugs": {"url": "https://github.com/bluelovers/symbol.inspect/issues"}, "repository": {"type": "git", "url": "git+https://github.com/bluelovers/symbol.inspect.git"}, "license": "ISC", "author": "bluelovers", "main": "index.js", "directories": {"test": "test"}, "scripts": {"coverage": "npx nyc yarn run test", "lint": "npx eslint **/*.ts", "ncu": "npx yarn-tool ncu -u", "npm:publish": "npm publish", "prepublish:lockfile": "npx sync-lockfile .", "prepublishOnly": "yarn run ncu && yarn run sort-package-json && yarn run test", "postpublish": "git commit -m \"chore(release): publish\" .", "sort-package-json": "npx yarn-tool sort", "test": "echo \"Error: no test specified\"", "test:mocha": "npx mocha --require ts-node/register \"!(node_modules)/**/*.{test,spec}.{ts,tsx}\"", "tsc:default": "tsc -p tsconfig.json", "tsc:esm": "tsc -p tsconfig.esm.json"}, "dependencies": {}, "devDependencies": {"@bluelovers/tsconfig": "^1.0.19", "@types/node": "^13.9.3"}}