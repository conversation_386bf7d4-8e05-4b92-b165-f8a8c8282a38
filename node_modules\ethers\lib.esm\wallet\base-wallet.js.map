{"version": 3, "file": "base-wallet.js", "sourceRoot": "", "sources": ["../../src.ts/wallet/base-wallet.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACjE,OAAO,EACH,iBAAiB,EAAE,WAAW,EAAE,gBAAgB,EACnD,MAAM,kBAAkB,CAAC;AAC1B,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAC;AACpE,OAAO,EAAE,cAAc,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtE,OAAO,EACH,gBAAgB,EAAE,SAAS,EAAE,iBAAiB,EAAE,MAAM,EAAE,cAAc,EACzE,MAAM,mBAAmB,CAAC;AAU3B;;;;;;;;;;GAUG;AACH,MAAM,OAAO,UAAW,SAAQ,cAAc;IAC1C;;OAEG;IACM,OAAO,CAAU;IAEjB,WAAW,CAAa;IAEjC;;;;;;OAMG;IACH,YAAY,UAAsB,EAAE,QAA0B;QAC1D,KAAK,CAAC,QAAQ,CAAC,CAAC;QAEhB,cAAc,CAAC,UAAU,IAAI,OAAM,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,UAAU,EAAE,qBAAqB,EAAE,YAAY,EAAE,cAAc,CAAC,CAAC;QAE1H,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC;QAE9B,MAAM,OAAO,GAAG,cAAc,CAAC,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;QAC1D,gBAAgB,CAAa,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,2DAA2D;IAC3D,iBAAiB;IAEjB;;OAEG;IACH,IAAI,UAAU,KAAiB,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;IAEzD;;OAEG;IACH,IAAI,UAAU,KAAa,OAAO,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC;IAE/D,KAAK,CAAC,UAAU,KAAsB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAE5D,OAAO,CAAC,QAAyB;QAC7B,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAsB;QACxC,EAAE,GAAG,WAAW,CAAC,EAAE,CAAC,CAAC;QAErB,sDAAsD;QACtD,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,GAAG,MAAM,iBAAiB,CAAC;YACzC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,EAAE,EAAE,IAAI,CAAC,CAAA,CAAC,CAAC,SAAS,CAAC;YACpD,IAAI,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC,EAAE,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA,CAAC,CAAC,SAAS,CAAC;SAC7D,CAAC,CAAC;QAEH,IAAI,EAAE,IAAI,IAAI,EAAE;YAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;SAAE;QAC/B,IAAI,IAAI,IAAI,IAAI,EAAE;YAAE,EAAE,CAAC,IAAI,GAAG,IAAI,CAAC;SAAE;QAErC,IAAI,EAAE,CAAC,IAAI,IAAI,IAAI,EAAE;YACjB,cAAc,CAAC,UAAU,CAAS,CAAC,EAAE,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,OAAO,EACzD,mCAAmC,EAAE,SAAS,EAAE,EAAE,CAAC,IAAI,CAAC,CAAC;YAC7D,OAAO,EAAE,CAAC,IAAI,CAAC;SAClB;QAED,wBAAwB;QACxB,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAA0B,EAAE,CAAC,CAAC;QAC1D,GAAG,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC;QAEvD,OAAO,GAAG,CAAC,UAAU,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAA4B;QAC1C,OAAO,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;IACzC,CAAC;IAED,kEAAkE;IAClE,4BAA4B;IAC5B;;OAEG;IACH,eAAe,CAAC,OAA4B;QACxC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC,CAAC,UAAU,CAAC;IACjE,CAAC;IAED;;OAEG;IACH,aAAa,CAAC,IAA0B;QACpC,cAAc,CAAC,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,EAC9C,mCAAmC,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;QAE7D,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,CAAC;QAChE,OAAO,MAAM,CAAC,MAAM,CAAC,EAAG,EAAE;YACtB,OAAO,EAAE,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;YACjC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC;YACjC,OAAO,EAAE,SAAS,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,CAAC;SACxC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,IAA0B;QACtC,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAG,EAAE,IAAI,EAAE;YAC5B,OAAO,EAAE,MAAM,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC;SACpD,CAAC,CAAC;QACH,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC,CAAC;IACtE,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAuB,EAAE,KAA4C,EAAE,KAA0B;QAEjH,yBAAyB;QACzB,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,YAAY,CAAC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,IAAY,EAAE,EAAE;YAC/F,sDAAsD;YACtD,yBAAyB;YAEzB,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE,6CAA6C,EAAE,uBAAuB,EAAE;gBAClG,SAAS,EAAE,aAAa;gBACxB,IAAI,EAAE,EAAE,IAAI,EAAE;aACjB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,CAAC,OAAO,IAAI,IAAI,EAAE,uBAAuB,EAAE,mBAAmB,EAAE;gBAClE,KAAK,EAAE,IAAI;aACd,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC,CAAC;QAEH,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,KAAK,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC;IAC5G,CAAC;CACJ"}