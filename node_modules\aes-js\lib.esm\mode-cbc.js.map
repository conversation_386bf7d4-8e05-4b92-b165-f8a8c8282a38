{"version": 3, "file": "mode-cbc.js", "sourceRoot": "", "sources": ["../src.ts/mode-cbc.ts"], "names": [], "mappings": "AAAA,wBAAwB;;;;;;;;;;;;;AAExB,OAAO,EAAE,eAAe,EAAE,MAAM,WAAW,CAAC;AAE5C,MAAM,OAAO,GAAI,SAAQ,eAAe;IAItC,YAAY,GAAe,EAAE,EAAe;QAC1C,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QAJzB,0BAAgB;QAChB,iCAAuB;QAKrB,IAAI,EAAE,EAAE;YACN,IAAI,EAAE,CAAC,MAAM,GAAG,EAAE,EAAE;gBAClB,MAAM,IAAI,SAAS,CAAC,oCAAoC,CAAC,CAAC;aAC3D;YACD,uBAAA,IAAI,WAAO,IAAI,UAAU,CAAC,EAAE,CAAC,MAAA,CAAC;SAC/B;aAAM;YACL,uBAAA,IAAI,WAAO,IAAI,UAAU,CAAC,EAAE,CAAC,MAAA,CAAC;SAC/B;QAED,uBAAA,IAAI,kBAAc,IAAI,CAAC,EAAE,MAAA,CAAC;IAC5B,CAAC;IAED,IAAI,EAAE,KAAiB,OAAO,IAAI,UAAU,CAAC,uBAAA,IAAI,eAAI,CAAC,CAAC,CAAC,CAAC;IAEzD,OAAO,CAAC,SAAqB;QAC3B,IAAI,SAAS,CAAC,MAAM,GAAG,EAAE,EAAE;YACzB,MAAM,IAAI,SAAS,CAAC,uDAAuD,CAAC,CAAC;SAC9E;QAED,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC3B,uBAAA,IAAI,sBAAW,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACxC;YAED,uBAAA,IAAI,kBAAc,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,uBAAA,IAAI,sBAAW,CAAC,MAAA,CAAC;YACpD,UAAU,CAAC,GAAG,CAAC,uBAAA,IAAI,sBAAW,EAAE,CAAC,CAAC,CAAC;SACpC;QAED,OAAO,UAAU,CAAC;IACpB,CAAC;IAED,OAAO,CAAC,UAAsB;QAC5B,IAAI,UAAU,CAAC,MAAM,GAAG,EAAE,EAAE;YACxB,MAAM,IAAI,SAAS,CAAC,wDAAwD,CAAC,CAAC;SACjF;QAED,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YAC5C,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC;YAE/D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;gBAC3B,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,GAAG,uBAAA,IAAI,sBAAW,CAAC,CAAC,CAAC,CAAC;gBACjD,uBAAA,IAAI,sBAAW,CAAC,CAAC,CAAC,GAAG,UAAU,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;aACxC;SACJ;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;CACF"}