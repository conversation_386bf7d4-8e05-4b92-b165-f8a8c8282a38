{"name": "querystring", "id": "querystring", "version": "0.2.1", "description": "Node's querystring module for all engines.", "keywords": ["commonjs", "query", "querystring"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/Gozala/querystring.git", "web": "https://github.com/Gozala/querystring"}, "bugs": {"url": "http://github.com/Gozala/querystring/issues/"}, "devDependencies": {"test": "~0.x.0", "retape": "~0.x.0", "tape": "~0.1.5"}, "engines": {"node": ">=0.4.x"}, "scripts": {"test": "npm run test-node && npm run test-tap", "test-node": "node ./test/common-index.js", "test-tap": "node ./test/tap-index.js"}, "license": "MIT"}