{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["index.ts"], "names": [], "mappings": ";AAAA;;;;;GAKG;AACH,MAAM,aAAa,GAAG,MAAM,CAAC,GAAG,CAAC,4BAA4B,CAAC,CAAC;AAC/D,iBAAS,aAAa,CAAC", "sourcesContent": ["/**\n * <symbol> that can be used to declare custom inspect functions.\n *\n * same as Symbol.for('nodejs.util.inspect.custom')\n * same as util.inspect.custom\n */\nconst SymbolInspect = Symbol.for('nodejs.util.inspect.custom');\nexport = SymbolInspect;\n"]}