/**
 * Copyright (c) Whales Corp.
 * All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/// <reference types="node" />
import inspectSymbol from 'symbol.inspect';
export declare class Address {
    static isAddress(src: any): src is Address;
    static isFriendly(source: string): boolean;
    static isRaw(source: string): boolean;
    static normalize(source: string | Address): string;
    static parse(source: string): Address;
    static parseRaw(source: string): Address;
    static parseFriendly(source: string | Buffer): {
        isBounceable: boolean;
        isTestOnly: boolean;
        address: Address;
    };
    readonly workChain: number;
    readonly hash: Buffer;
    constructor(workChain: number, hash: Buffer);
    toRawString: () => string;
    equals(src: Address): boolean;
    toRaw: () => Buffer;
    toStringBuffer: (args?: {
        bounceable?: boolean;
        testOnly?: boolean;
    }) => Buffer;
    toString: (args?: {
        urlSafe?: boolean;
        bounceable?: boolean;
        testOnly?: boolean;
    }) => string;
    [inspectSymbol]: () => string;
}
export declare function address(src: string): Address;
