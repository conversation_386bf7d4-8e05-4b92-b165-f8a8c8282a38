{"version": 3, "file": "abstract-signer.js", "sourceRoot": "", "sources": ["../../src.ts/providers/abstract-signer.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AACH,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAC;AACrD,OAAO,EAAE,WAAW,EAAE,MAAM,yBAAyB,CAAC;AACtD,OAAO,EACH,gBAAgB,EAAE,SAAS,EAAE,iBAAiB,EAC9C,MAAM,EAAE,cAAc,EACzB,MAAM,mBAAmB,CAAC;AAE3B,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAY5C,SAAS,aAAa,CAAC,MAAsB,EAAE,SAAiB;IAC5D,IAAI,MAAM,CAAC,QAAQ,EAAE;QAAE,OAAO,MAAM,CAAC,QAAQ,CAAC;KAAE;IAChD,MAAM,CAAC,KAAK,EAAE,kBAAkB,EAAE,uBAAuB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;AAC9E,CAAC;AAED,KAAK,UAAU,QAAQ,CAAC,MAAsB,EAAE,EAAsB;IAClE,IAAI,GAAG,GAAQ,WAAW,CAAC,EAAE,CAAC,CAAC;IAE/B,IAAI,GAAG,CAAC,EAAE,IAAI,IAAI,EAAE;QAAE,GAAG,CAAC,EAAE,GAAG,cAAc,CAAC,GAAG,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;KAAE;IAEhE,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE;QAClB,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC;QACtB,GAAG,CAAC,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC;YACnB,MAAM,CAAC,UAAU,EAAE;YACnB,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC;SAC/B,CAAC,CAAC,IAAI,CAAC,CAAC,CAAE,OAAO,EAAE,IAAI,CAAE,EAAE,EAAE;YAC1B,cAAc,CAAC,OAAO,CAAC,WAAW,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,EACvD,2BAA2B,EAAE,SAAS,EAAE,IAAI,CAAC,CAAC;YAClD,OAAO,OAAO,CAAC;QACnB,CAAC,CAAC,CAAC;KACN;SAAM;QACH,GAAG,CAAC,IAAI,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;KAClC;IAED,OAAO,MAAM,iBAAiB,CAAC,GAAG,CAAC,CAAC;AACxC,CAAC;AAGD;;;;;GAKG;AACH,MAAM,OAAgB,cAAc;IAChC;;OAEG;IACM,QAAQ,CAAK;IAEtB;;OAEG;IACH,YAAY,QAAY;QACpB,gBAAgB,CAAiB,IAAI,EAAE,EAAE,QAAQ,EAAE,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;IAC7E,CAAC;IAeD,KAAK,CAAC,QAAQ,CAAC,QAAmB;QAC9B,OAAO,aAAa,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC,mBAAmB,CAAC,MAAM,IAAI,CAAC,UAAU,EAAE,EAAE,QAAQ,CAAC,CAAC;IAC7G,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,EAAsB;QACrC,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QACrC,OAAO,GAAG,CAAC;IACf,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,EAAsB;QAC5C,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,EAAE,qBAAqB,CAAC,CAAC;QAE5D,MAAM,GAAG,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;QAErC,IAAI,GAAG,CAAC,KAAK,IAAI,IAAI,EAAE;YACnB,GAAG,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;SAC9C;QAED,IAAI,GAAG,CAAC,QAAQ,IAAI,IAAI,EAAE;YACtB,GAAG,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;SAC9C;QAED,wBAAwB;QACxB,MAAM,OAAO,GAAG,MAAiB,CAAC,IAAI,CAAC,QAAQ,CAAE,CAAC,UAAU,EAAE,CAAC;QAC/D,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,EAAE;YACrB,MAAM,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;YACvC,cAAc,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO,EAAE,8BAA8B,EAAE,YAAY,EAAE,EAAE,CAAC,OAAO,CAAC,CAAC;SACzG;aAAM;YACH,GAAG,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;SACjC;QAED,2DAA2D;QAC3D,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,IAAI,GAAG,CAAC,oBAAoB,IAAI,IAAI,CAAC,CAAC;QAClF,IAAI,GAAG,CAAC,QAAQ,IAAI,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,UAAU,CAAC,EAAE;YACxD,cAAc,CAAC,KAAK,EAAE,8CAA8C,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;SACnF;aAAM,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,CAAC,IAAI,UAAU,EAAE;YACzD,cAAc,CAAC,KAAK,EAAE,2EAA2E,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;SAChH;QAED,IAAI,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,IAAI,IAAI,IAAI,GAAG,CAAC,oBAAoB,IAAI,IAAI,CAAC,EAAE;YACxG,sDAAsD;YACtD,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;SAEhB;aAAM,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE;YACzC,0CAA0C;YAE1C,8CAA8C;YAC9C,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;YAE5C,MAAM,CAAC,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE,mCAAmC,EAAE,uBAAuB,EAAE;gBAC3F,SAAS,EAAE,aAAa;aAAE,CAAC,CAAC;YAEhC,4BAA4B;YAC5B,IAAI,GAAG,CAAC,QAAQ,IAAI,IAAI,EAAE;gBAAE,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;aAAE;SAEjE;aAAM;YAEH,8CAA8C;YAC9C,MAAM,OAAO,GAAG,MAAM,QAAQ,CAAC,UAAU,EAAE,CAAC;YAE5C,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE;gBAClB,kEAAkE;gBAElE,IAAI,OAAO,CAAC,YAAY,IAAI,IAAI,IAAI,OAAO,CAAC,oBAAoB,IAAI,IAAI,EAAE;oBACtE,iCAAiC;oBAEjC,4CAA4C;oBAC5C,IAAI,GAAG,CAAC,iBAAiB,IAAI,GAAG,CAAC,iBAAiB,CAAC,MAAM,EAAE;wBACvD,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;qBAChB;yBAAM;wBACH,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;qBAChB;oBAED,IAAI,GAAG,CAAC,QAAQ,IAAI,IAAI,EAAE;wBACtB,yDAAyD;wBACzD,yCAAyC;wBACzC,MAAM,QAAQ,GAAG,GAAG,CAAC,QAAQ,CAAC;wBAC9B,OAAO,GAAG,CAAC,QAAQ,CAAC;wBACpB,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC;wBAC5B,GAAG,CAAC,oBAAoB,GAAG,QAAQ,CAAC;qBAEvC;yBAAM;wBACH,4BAA4B;wBAE5B,IAAI,GAAG,CAAC,YAAY,IAAI,IAAI,EAAE;4BAC1B,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;yBAC3C;wBAED,IAAI,GAAG,CAAC,oBAAoB,IAAI,IAAI,EAAE;4BAClC,GAAG,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;yBAC3D;qBACJ;iBAEJ;qBAAM,IAAI,OAAO,CAAC,QAAQ,IAAI,IAAI,EAAE;oBACjC,sCAAsC;oBAEtC,oDAAoD;oBACpD,MAAM,CAAC,CAAC,UAAU,EAAE,mCAAmC,EAAE,uBAAuB,EAAE;wBAC1E,SAAS,EAAE,qBAAqB;qBAAE,CAAC,CAAC;oBAE5C,4BAA4B;oBAC5B,IAAI,GAAG,CAAC,QAAQ,IAAI,IAAI,EAAE;wBACtB,GAAG,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;qBACnC;oBAED,+CAA+C;oBAC/C,wCAAwC;oBACxC,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC;iBAEjB;qBAAM;oBACF,4BAA4B;oBAC5B,MAAM,CAAC,KAAK,EAAE,mCAAmC,EAAE,uBAAuB,EAAE;wBACxE,SAAS,EAAE,mBAAmB;qBAAE,CAAC,CAAC;iBACzC;aAEJ;iBAAM,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,IAAI,GAAG,CAAC,IAAI,KAAK,CAAC,EAAE;gBAC3D,wCAAwC;gBAExC,4BAA4B;gBAC5B,IAAI,GAAG,CAAC,YAAY,IAAI,IAAI,EAAE;oBAC1B,GAAG,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;iBAC3C;gBAED,IAAI,GAAG,CAAC,oBAAoB,IAAI,IAAI,EAAE;oBAClC,GAAG,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,CAAC;iBAC3D;aACJ;SACJ;QAET,yDAAyD;QACzD,8BAA8B;QACtB,OAAO,MAAM,iBAAiB,CAAC,GAAG,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,KAA2B;QACnD,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC,EAAG,EAAE,KAAK,CAAC,CAAC;QAEvC,4CAA4C;QAC5C,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,OAAO,GAAG,CAAC,MAAM,aAAa,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC,UAAU,EAAE,CAAC,CAAC,OAAO,CAAC;SACjF;QAED,0DAA0D;QAE1D,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YAAE,IAAI,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,EAAE,CAAC;SAAE;QAE/D,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,EAAsB;QACpC,OAAO,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IACvF,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,EAAsB;QAC7B,OAAO,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAY;QAC1B,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,EAAE,aAAa,CAAC,CAAC;QACpD,OAAO,MAAM,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAsB;QACxC,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,EAAE,iBAAiB,CAAC,CAAC;QAExD,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAC,EAAE,CAAC,CAAC;QAC/C,OAAO,GAAG,CAAC,IAAI,CAAC;QAChB,MAAM,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACpC,OAAO,MAAM,QAAQ,CAAC,oBAAoB,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC;IAClF,CAAC;IAED,wCAAwC;IACxC,SAAS,CAAC,aAAmC;QACzC,MAAM,CAAC,KAAK,EAAE,+CAA+C,EAC3D,uBAAuB,EAAE,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC;IAC3D,CAAC;CAKJ;AAED;;;;;;;GAOG;AACH,MAAM,OAAO,UAAW,SAAQ,cAAc;IAC1C;;OAEG;IACM,OAAO,CAAU;IAE1B;;;OAGG;IACH,YAAY,OAAe,EAAE,QAA0B;QACnD,KAAK,CAAC,QAAQ,CAAC,CAAC;QAChB,gBAAgB,CAAa,IAAI,EAAE,EAAE,OAAO,EAAE,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,UAAU,KAAsB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;IAE5D,OAAO,CAAC,QAAyB;QAC7B,OAAO,IAAI,UAAU,CAAC,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAClD,CAAC;IAED,iBAAiB,CAAC,MAAc,EAAE,SAAiB;QAC/C,MAAM,CAAC,KAAK,EAAE,0BAA2B,MAAO,EAAE,EAAE,uBAAuB,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;IAChG,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,EAAsB;QACxC,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,iBAAiB,CAAC,CAAC;IAC9D,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAA4B;QAC1C,IAAI,CAAC,iBAAiB,CAAC,UAAU,EAAE,aAAa,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,MAAuB,EAAE,KAA4C,EAAE,KAA0B;QACjH,IAAI,CAAC,iBAAiB,CAAC,YAAY,EAAE,eAAe,CAAC,CAAC;IAC1D,CAAC;CACJ"}