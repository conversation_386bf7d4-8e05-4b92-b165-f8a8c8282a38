"use strict";
/**
 * Copyright (c) Whales Corp.
 * All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.findCommonPrefix = void 0;
function findCommonPrefix(src) {
    // Corner cases
    if (src.length === 0) {
        return '';
    }
    if (src.length === 1) {
        return src[0];
    }
    // Searching for prefix
    const sorted = [...src].sort();
    let size = 0;
    for (let i = 0; i < sorted[0].length; i++) {
        if (sorted[0][i] !== sorted[sorted.length - 1][i]) {
            break;
        }
        size++;
    }
    return src[0].slice(0, size);
}
exports.findCommonPrefix = findCommonPrefix;
