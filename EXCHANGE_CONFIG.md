# Dango Exchange Bot Configuration Guide

This bot is designed for the Dango ecosystem and supports multiple exchange configurations:

## Supported Exchanges

### 1. Dango Exchange (Primary)
- **Exchange Type**: `dango`
- **Features**: Token swaps only (no liquidity provision)
- **Configuration**: Set `EXCHANGE_TYPE=dango` in your .env file
- **Native Token**: DANGO

### 2. ZenithSwap (Legacy Support)
- **Exchange Type**: `zenith`
- **Features**: Token swaps + Liquidity provision
- **Configuration**: Set `EXCHANGE_TYPE=zenith` in your .env file

## Configuration

### Environment Variables

Add these to your `.env` file:

```env
# Exchange Configuration
EXCHANGE_TYPE=dango  # or "zenith"

# When using Dango (swap only), disable LP features
AUTO_ADDLP=false
```

### Key Differences

#### Dango Exchange (Primary)
- Only supports token swaps
- Liquidity provision is automatically disabled
- The bot will log a warning if `AUTO_ADDLP=true` is set with Dango
- Uses DANGO as the native token

#### ZenithSwap (Legacy)
- Supports both swaps and liquidity provision
- All features available
- Legacy support for existing configurations

## Contract Addresses

The bot automatically uses the correct contract addresses based on the exchange type:

### ZenithSwap Addresses
- Swap Router: `0x1A4DE519154Ae51200b0Ad7c90F7faC75547888a`
- Position Manager: `0xF8a1D4FF0f9b9Af7CE58E1fc1833688F3BFd6115`
- Factory: `0x7CE5b44F2d05babd29caE68557F52ab051265F01`
- Quoter: `0x00f2f47d1ed593Cf0AF0074173E9DF95afb0206C`

### Dango Addresses
- Swap Router: `0x1A4DE519154Ae51200b0Ad7c90F7faC75547888a` (update with actual Dango address)
- Position Manager: Not supported
- Factory: Not supported
- Quoter: Not supported

## Usage Examples

### For Dango Exchange (Swap Only)
```env
EXCHANGE_TYPE=dango
AUTO_SWAP=true
AUTO_ADDLP=false
NUMBER_SWAP=10
AMOUNT_SWAP=[0.1, 0.2]
```

### For ZenithSwap (Full Features)
```env
EXCHANGE_TYPE=zenith
AUTO_SWAP=true
AUTO_ADDLP=true
NUMBER_SWAP=10
NUMBER_ADDLP=5
AMOUNT_SWAP=[0.1, 0.2]
AMOUNT_ADDLP=[1, 15]
```

## Important Notes

1. **Dango Router Address**: Update the `DANGO_ADDRESSES.swapRouter` in `utils/constants.js` with the actual Dango router contract address.

2. **Automatic LP Skipping**: When using Dango exchange, the bot automatically skips liquidity provision operations and logs a warning message.

3. **Backward Compatibility**: If no `EXCHANGE_TYPE` is specified, the bot defaults to ZenithSwap for backward compatibility.

4. **Contract Validation**: The bot validates that the exchange supports the requested operations before executing them.
