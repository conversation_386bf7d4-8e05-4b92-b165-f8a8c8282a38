# Dango Exchange Bot

Automated trading bot for the Dango ecosystem, supporting token swaps and multi-exchange compatibility.

## Features

- 🔄 **Token Swaps**: Automated DANGO/USDC/USDT swapping
- 🎯 **Multi-Exchange Support**: Primary support for Dango exchange, legacy support for ZenithSwap
- 🔐 **Multi-Wallet**: Support for multiple wallets with proxy rotation
- ⚡ **Smart Routing**: Automatic contract address selection based on exchange type
- 🛡️ **Safety Features**: Balance checks, approval management, and error handling
- 📊 **Detailed Logging**: Comprehensive transaction logging and status updates

## Supported Exchanges

### Dango Exchange (Primary)
- **Type**: `dango`
- **Features**: Token swaps only
- **Native Token**: DANGO
- **Status**: ✅ Active

### ZenithSwap (Legacy)
- **Type**: `zenith` 
- **Features**: Token swaps + Liquidity provision
- **Status**: 🔧 Legacy support

## Quick Start

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd dango-exchange-bot
   npm install
   ```

2. **Configuration**
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Setup Wallets**
   - Add private keys to `privateKeys.txt` (one per line)
   - Add proxy addresses to `proxy.txt` (optional)

4. **Run the Bot**
   ```bash
   node main.js
   ```

## Configuration

### Environment Variables (.env)

```env
# Exchange Configuration
EXCHANGE_TYPE=dango          # "dango" or "zenith"

# Bot Settings
AUTO_SWAP=true              # Enable automatic swapping
AUTO_ADDLP=false           # Disable for Dango (swap only)
AUTO_SEND=true             # Enable token transfers

# Swap Configuration
NUMBER_SWAP=10             # Number of swaps per session
AMOUNT_SWAP=[0.1, 0.2]     # Swap amount range

# Network
RPC_URL=https://evmrpc-testnet.0g.ai
CHAIN_ID=688688

# Delays (seconds)
DELAY_BETWEEN_REQUESTS=[1, 5]
DELAY_START_BOT=[1, 15]
```

### File Structure

```
dango-exchange-bot/
├── main.js                 # Main application
├── config/
│   ├── config.js          # Configuration management
│   └── userAgents.js      # User agent rotation
├── utils/
│   ├── constants.js       # Contract addresses & utilities
│   ├── contract.js        # Smart contract interactions
│   ├── swap.js           # Swap service
│   └── liqulity.js       # Liquidity operations (legacy)
├── privateKeys.txt        # Wallet private keys
├── proxy.txt             # Proxy addresses (optional)
└── .env                  # Environment configuration
```

## Important Notes

1. **Dango Router Address**: Update the actual Dango router contract address in `utils/constants.js`
2. **Security**: Never commit private keys or sensitive data to version control
3. **Testnet**: Currently configured for testnet - update for mainnet use
4. **Gas Fees**: Monitor gas prices and adjust limits as needed

## Support

For issues and questions:
- Check the configuration guide: `EXCHANGE_CONFIG.md`
- Review logs for error details
- Ensure proper wallet funding and network connectivity

## License

This project is for educational and development purposes.
