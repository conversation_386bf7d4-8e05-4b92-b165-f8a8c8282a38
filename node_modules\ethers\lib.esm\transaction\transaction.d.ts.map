{"version": 3, "file": "transaction.d.ts", "sourceRoot": "", "sources": ["../../src.ts/transaction/transaction.ts"], "names": [], "mappings": "AAGA,OAAO,EACgB,SAAS,EAC/B,MAAM,oBAAoB,CAAC;AAU5B,OAAO,KAAK,EAAE,YAAY,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AACjE,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAExD,OAAO,KAAK,EACR,UAAU,EAAE,aAAa,EAAE,aAAa,EAAE,iBAAiB,EAC9D,MAAM,YAAY,CAAC;AAepB;;;;GAIG;AACH,MAAM,WAAW,eAAe,CAAC,CAAC,GAAG,MAAM;IACvC;;OAEG;IACH,IAAI,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAErB;;OAEG;IACH,EAAE,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC;IAEd;;OAEG;IACH,IAAI,CAAC,EAAE,IAAI,GAAG,CAAC,CAAC;IAEhB;;OAEG;IACH,KAAK,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAEtB;;OAEG;IACH,QAAQ,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC;IAE/B;;OAEG;IACH,QAAQ,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC;IAE/B;;OAEG;IACH,oBAAoB,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC;IAE3C;;OAEG;IACH,YAAY,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC;IAEnC;;OAEG;IACH,IAAI,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAErB;;OAEG;IACH,KAAK,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC;IAE5B;;OAEG;IACH,OAAO,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC;IAE9B;;OAEG;IACH,IAAI,CAAC,EAAE,IAAI,GAAG,MAAM,CAAC;IAErB;;OAEG;IACH,SAAS,CAAC,EAAE,IAAI,GAAG,aAAa,CAAC;IAEjC;;OAEG;IACH,UAAU,CAAC,EAAE,IAAI,GAAG,aAAa,CAAC;IAElC;;OAEG;IACH,gBAAgB,CAAC,EAAE,IAAI,GAAG,YAAY,CAAC;IAEvC;;OAEG;IACH,mBAAmB,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;IAE3C;;OAEG;IACH,KAAK,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAA;IAE9B;;;;;;OAMG;IACH,GAAG,CAAC,EAAE,IAAI,GAAG,cAAc,CAAC;IAE5B;;OAEG;IACH,iBAAiB,CAAC,EAAE,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,CAAC;CACnD;AAED;;;;;GAKG;AACH,MAAM,WAAW,IAAI;IACjB,IAAI,EAAE,MAAM,CAAC;IACb,KAAK,EAAE,MAAM,CAAC;IACd,UAAU,EAAE,MAAM,CAAC;CACtB;AAED;;;;;;GAMG;AACH,MAAM,MAAM,QAAQ,GAAG,SAAS,GAAG;IAC/B,IAAI,EAAE,SAAS,CAAC;IAChB,KAAK,EAAE,SAAS,CAAC;IACjB,UAAU,EAAE,SAAS,CAAC;CACzB,CAAC;AAEF;;;GAGG;AACH,MAAM,WAAW,UAAU;IACvB,mBAAmB,EAAE,CAAC,IAAI,EAAE,UAAU,KAAK,UAAU,CAAC;IACtD,mBAAmB,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,KAAK,UAAU,CAAC;CACjF;AAED;;;;;;;;GAQG;AACH,MAAM,MAAM,cAAc,GAAI,UAAU,GAAG;IAEvC,mBAAmB,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,MAAM,CAAC;IAC9C,mBAAmB,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,KAAK,MAAM,CAAC;CACrE,GAAG;IAEA,mBAAmB,EAAE,CAAC,IAAI,EAAE,MAAM,KAAK,MAAM,GAAG,UAAU,CAAC;IAC3D,gBAAgB,EAAE,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,KAAK,MAAM,GAAG,UAAU,CAAC;CAC/E,CAAC;AA6gBF;;;;;;;;;;;;GAYG;AACH,qBAAa,WAAY,YAAW,eAAe,CAAC,MAAM,CAAC;;IAmBvD;;;;;OAKG;IACH,IAAI,IAAI,IAAI,IAAI,GAAG,MAAM,CAAuB;IAChD,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,GAAG,MAAM,GAAG,MAAM,EAuBrC;IAED;;OAEG;IACH,IAAI,QAAQ,IAAI,IAAI,GAAG,MAAM,CAU5B;IAED;;;OAGG;IACH,IAAI,EAAE,IAAI,IAAI,GAAG,MAAM,CAItB;IACD,IAAI,EAAE,CAAC,KAAK,EAAE,IAAI,GAAG,MAAM,EAE1B;IAED;;OAEG;IACH,IAAI,KAAK,IAAI,MAAM,CAAwB;IAC3C,IAAI,KAAK,CAAC,KAAK,EAAE,YAAY,EAA8C;IAE3E;;OAEG;IACH,IAAI,QAAQ,IAAI,MAAM,CAA2B;IACjD,IAAI,QAAQ,CAAC,KAAK,EAAE,YAAY,EAAwC;IAExE;;;;;OAKG;IACH,IAAI,QAAQ,IAAI,IAAI,GAAG,MAAM,CAI5B;IACD,IAAI,QAAQ,CAAC,KAAK,EAAE,IAAI,GAAG,YAAY,EAEtC;IAED;;;OAGG;IACH,IAAI,oBAAoB,IAAI,IAAI,GAAG,MAAM,CAOxC;IACD,IAAI,oBAAoB,CAAC,KAAK,EAAE,IAAI,GAAG,YAAY,EAElD;IAED;;;OAGG;IACH,IAAI,YAAY,IAAI,IAAI,GAAG,MAAM,CAOhC;IACD,IAAI,YAAY,CAAC,KAAK,EAAE,IAAI,GAAG,YAAY,EAE1C;IAED;;;OAGG;IACH,IAAI,IAAI,IAAI,MAAM,CAAuB;IACzC,IAAI,IAAI,CAAC,KAAK,EAAE,SAAS,EAAkC;IAE3D;;OAEG;IACH,IAAI,KAAK,IAAI,MAAM,CAAwB;IAC3C,IAAI,KAAK,CAAC,KAAK,EAAE,YAAY,EAE5B;IAED;;OAEG;IACH,IAAI,OAAO,IAAI,MAAM,CAA0B;IAC/C,IAAI,OAAO,CAAC,KAAK,EAAE,YAAY,EAAuC;IAEtE;;OAEG;IACH,IAAI,SAAS,IAAI,IAAI,GAAG,SAAS,CAA8B;IAC/D,IAAI,SAAS,CAAC,KAAK,EAAE,IAAI,GAAG,aAAa,EAExC;IAED;;;;;OAKG;IACH,IAAI,UAAU,IAAI,IAAI,GAAG,UAAU,CAWlC;IACD,IAAI,UAAU,CAAC,KAAK,EAAE,IAAI,GAAG,aAAa,EAEzC;IAED,IAAI,iBAAiB,IAAI,IAAI,GAAG,KAAK,CAAC,aAAa,CAAC,CAUnD;IACD,IAAI,iBAAiB,CAAC,KAAK,EAAE,IAAI,GAAG,KAAK,CAAC,iBAAiB,CAAC,EAG3D;IAED;;OAEG;IACH,IAAI,gBAAgB,IAAI,IAAI,GAAG,MAAM,CAIpC;IACD,IAAI,gBAAgB,CAAC,KAAK,EAAE,IAAI,GAAG,YAAY,EAE9C;IAED;;OAEG;IACH,IAAI,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAM9C;IACD,IAAI,mBAAmB,CAAC,KAAK,EAAE,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,EASlD;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;OA2BG;IACH,IAAI,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAG9B;IACD,IAAI,KAAK,CAAC,MAAM,EAAE,IAAI,GAAG,KAAK,CAAC,QAAQ,CAAC,EAiDvC;IAED,IAAI,GAAG,IAAI,IAAI,GAAG,UAAU,CAAsB;IAClD,IAAI,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,cAAc,EAMjC;IAED;;OAEG;;IAqBH;;OAEG;IACH,IAAI,IAAI,IAAI,IAAI,GAAG,MAAM,CAGxB;IAED;;;;;OAKG;IACH,IAAI,YAAY,IAAI,MAAM,CAEzB;IAED;;OAEG;IACH,IAAI,IAAI,IAAI,IAAI,GAAG,MAAM,CAGxB;IAED;;OAEG;IACH,IAAI,aAAa,IAAI,IAAI,GAAG,MAAM,CAGjC;IAED;;;;;OAKG;IACH,QAAQ,IAAI,IAAI,IAAI,CAAC,WAAW,GAAG;QAAE,IAAI,EAAE,MAAM,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAC;QAAC,IAAI,EAAE,MAAM,CAAC;QAAC,SAAS,EAAE,SAAS,CAAA;KAAE,CAAC;IAwB1G;;;;;OAKG;IACH,IAAI,UAAU,IAAI,MAAM,CAEvB;IAED;;;;;OAKG;IACH,IAAI,kBAAkB,IAAI,MAAM,CAE/B;IAED;;;OAGG;IACH,SAAS,IAAI,MAAM;IAUnB;;;OAGG;IACH,UAAU,IAAI,KAAK,CAAC,MAAM,CAAC;IAuD3B;;;;;;OAMG;IACH,QAAQ,IAAI,IAAI,IAAI,CAAC,WAAW,GAAG;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAA;KAAE,CAAC;IAIjE;;;;;;OAMG;IACH,QAAQ,IAAI,IAAI,IAAI,CAAC,WAAW,GAAG;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,QAAQ,EAAE,MAAM,CAAC;QAAC,UAAU,EAAE,UAAU,CAAA;KAAE,CAAC;IAIzF;;;;;;OAMG;IACH,QAAQ,IAAI,IAAI,IAAI,CAAC,WAAW,GAAG;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,UAAU,EAAE,UAAU,CAAC;QAAC,YAAY,EAAE,MAAM,CAAC;QAAC,oBAAoB,EAAE,MAAM,CAAA;KAAE,CAAC;IAI3H;;;;;;OAMG;IACH,QAAQ,IAAI,IAAI,IAAI,CAAC,WAAW,GAAG;QAAE,IAAI,EAAE,CAAC,CAAC;QAAC,EAAE,EAAE,MAAM,CAAC;QAAC,UAAU,EAAE,UAAU,CAAC;QAAC,YAAY,EAAE,MAAM,CAAC;QAAC,oBAAoB,EAAE,MAAM,CAAC;QAAC,gBAAgB,EAAE,MAAM,CAAC;QAAC,mBAAmB,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;KAAE,CAAC;IAIrM;;OAEG;IACH,KAAK,IAAI,WAAW;IAIpB;;OAEG;IACH,MAAM,IAAI,GAAG;IAuBb;;;OAGG;IACH,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC,GAAG,WAAW;CAyDlE"}